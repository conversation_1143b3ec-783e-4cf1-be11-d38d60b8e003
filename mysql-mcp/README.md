# MySQL MCP

这是一个MySQL数据库访问的MCP（Model Context Protocol）服务器，允许AI助手（如Cline或Cursor）直接访问MySQL数据库。

## 功能

- **query_data**: 执行SQL查询并返回结果
- **describe_table**: 获取表结构信息
- **create_table**: 创建新表

## 安全限制

为了保护数据库安全，此MCP服务器仅允许以下SQL操作：

- SELECT：查询数据
- DESCRIBE/DESC：查看表结构
- SHOW：显示数据库对象
- CREATE TABLE：创建新表

不允许执行修改数据的操作，如INSERT、UPDATE、DELETE等。

## 安装步骤

### 1. 构建项目

```bash
cd mysql-mcp
npm install
npm run build
```

### 2. 配置MCP服务器

编辑`mcp-config.json`文件，设置MySQL连接信息：

```json
{
  "mcpServers": {
    "mysql": {
      "command": "node",
      "args": ["./build/index.js"],
      "env": {
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "your_username",
        "MYSQL_PASSWORD": "your_password",
        "MYSQL_DATABASE": "your_database"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

### 3. 添加到Cline或Cursor

#### Cline

将配置添加到Cline的MCP设置文件：

```bash
# Linux
cp mcp-config.json ~/.cursor-server/data/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json

# macOS
cp mcp-config.json ~/Library/Application\ Support/Cursor/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json

# Windows
cp mcp-config.json %APPDATA%\\Cursor\\User\\globalStorage\\saoudrizwan.claude-dev\\settings\\cline_mcp_settings.json
```

#### Cursor

将配置添加到Cursor的MCP设置文件：

```bash
# macOS
cp mcp-config.json ~/Library/Application\ Support/Claude/claude_desktop_config.json

# Windows
cp mcp-config.json %APPDATA%\\Claude\\claude_desktop_config.json
```

## 使用示例

一旦配置完成，你可以在Cline或Cursor中使用以下命令：

### 查询数据

```
查询所有用户数据
```

AI助手将使用`query_data`工具执行：

```sql
SELECT * FROM users
```

### 查看表结构

```
显示用户表的结构
```

AI助手将使用`describe_table`工具执行：

```sql
DESCRIBE users
```

### 创建新表

```
创建一个新的产品表，包含id、名称、价格和库存字段
```

AI助手将使用`create_table`工具执行：

```sql
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  stock INT DEFAULT 0
)
```

## 故障排除

如果连接数据库失败，请检查：

1. MySQL服务是否正在运行
2. 连接信息（主机、端口、用户名、密码）是否正确
3. 用户是否有足够的权限访问指定的数据库

## 开发

要修改或扩展此MCP服务器，请编辑`src/index.ts`文件，然后重新构建项目。
