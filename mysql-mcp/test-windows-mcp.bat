@echo off
echo MySQL MCP Windows Test Script
echo ============================
echo.
echo This script will test the MySQL MCP server in Windows environment.
echo.

REM Set MySQL connection information
set MYSQL_HOST=************
set MYSQL_PORT=3306
set MYSQL_USER=qiubin
set MYSQL_PASSWORD=qiubin@0306%%chz
set MYSQL_DATABASE=erp_dev

echo Testing MySQL MCP server...
echo.
echo Host: %MYSQL_HOST%
echo Port: %MYSQL_PORT%
echo User: %MYSQL_USER%
echo Database: %MYSQL_DATABASE%
echo.

REM Run the MCP server
echo Starting MySQL MCP server...
wsl -d Ubuntu-20.04 -e node /home/<USER>/projects/mysql-mcp/mysql-mcp/build/index.js

echo.
echo If you see no errors, the MySQL MCP server is working correctly.
echo Press any key to exit...
pause > nul
