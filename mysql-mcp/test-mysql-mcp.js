#!/usr/bin/env node

/**
 * MySQL MCP Test Script
 * 
 * This script tests the MySQL MCP by executing a simple query.
 * It uses the same configuration as the MCP server.
 */

import * as mysql from 'mysql2/promise';

// MySQL connection configuration from environment variables
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || '',
};

async function testConnection() {
  console.log('MySQL MCP Test Script');
  console.log('=====================');
  console.log('Testing connection to MySQL database...');
  console.log(`Host: ${dbConfig.host}`);
  console.log(`Port: ${dbConfig.port}`);
  console.log(`User: ${dbConfig.user}`);
  console.log(`Database: ${dbConfig.database}`);
  console.log('=====================');

  try {
    // Create connection
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connection successful!');

    // Execute SHOW TABLES query
    console.log('\nExecuting SHOW TABLES query...');
    const [rows] = await connection.query('SHOW TABLES');
    
    console.log('\nTables in database:');
    if (rows.length === 0) {
      console.log('No tables found.');
    } else {
      rows.forEach(row => {
        // Get the first value in the row (table name)
        const tableName = Object.values(row)[0];
        console.log(`- ${tableName}`);
      });
    }

    // Close connection
    await connection.end();
    console.log('\n✅ Test completed successfully!');
    return true;
  } catch (error) {
    console.error('\n❌ Test failed:');
    console.error(error);
    return false;
  }
}

// Run the test
testConnection()
  .then(success => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
