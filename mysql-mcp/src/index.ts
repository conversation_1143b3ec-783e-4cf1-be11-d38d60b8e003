#!/usr/bin/env node

/**
 * MySQL MCP Server
 * 
 * This MCP server provides tools to interact with a MySQL database:
 * - query_data: Execute SQL queries and return results
 * - describe_table: Get table structure information
 * - create_table: Create a new table
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import * as mysql from 'mysql2/promise';
// MySQL connection configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || '************',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'qiubin',
  password: process.env.MYSQL_PASSWORD || 'qiubin@0306%chz',
  database: process.env.MYSQL_DATABASE || 'erp_dev',
};

// Maximum number of rows to return
const MAX_ROWS = 1000;

/**
 * MySQL connection pool
 */
let pool: mysql.Pool | null = null;

/**
 * Flag to indicate if we're in mock mode
 */
let mockMode = false;

/**
 * Initialize MySQL connection pool
 */
async function initializeDatabase() {
  // 记录数据库连接配置（不包含敏感信息）
  console.error('[Setup] Initializing MySQL connection pool with config:', {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    database: dbConfig.database,
    // 不记录密码
  });
  
  try {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    });
    
    // Test connection
    const connection = await pool.getConnection();
    console.error('[Setup] MySQL connection successful');
    connection.release();
    
    return true;
  } catch (error) {
    console.error('[Error] Failed to connect to MySQL:', error);
    console.error('[Setup] Falling back to mock mode');
    mockMode = true;
    return true; // Return true to allow server to start in mock mode
  }
}

/**
 * Type definitions for mock data
 */
type TableStructure = Array<{
  Field: string;
  Type: string;
  Null: string;
  Key: string;
  Default: string | null;
  Extra: string;
}>;

type TableStructures = {
  [tableName: string]: TableStructure;
};

type SampleData = {
  [tableName: string]: Array<Record<string, any>>;
};

/**
 * Mock data for testing
 */
const mockData = {
  tables: ['users', 'products', 'orders'],
  tableStructures: {
    users: [
      { Field: 'id', Type: 'int', Null: 'NO', Key: 'PRI', Default: null, Extra: 'auto_increment' },
      { Field: 'username', Type: 'varchar(50)', Null: 'NO', Key: '', Default: null, Extra: '' },
      { Field: 'email', Type: 'varchar(100)', Null: 'NO', Key: '', Default: null, Extra: '' },
      { Field: 'created_at', Type: 'timestamp', Null: 'NO', Key: '', Default: 'CURRENT_TIMESTAMP', Extra: '' }
    ],
    products: [
      { Field: 'id', Type: 'int', Null: 'NO', Key: 'PRI', Default: null, Extra: 'auto_increment' },
      { Field: 'name', Type: 'varchar(100)', Null: 'NO', Key: '', Default: null, Extra: '' },
      { Field: 'price', Type: 'decimal(10,2)', Null: 'NO', Key: '', Default: null, Extra: '' },
      { Field: 'stock', Type: 'int', Null: 'NO', Key: '', Default: '0', Extra: '' }
    ],
    orders: [
      { Field: 'id', Type: 'int', Null: 'NO', Key: 'PRI', Default: null, Extra: 'auto_increment' },
      { Field: 'user_id', Type: 'int', Null: 'NO', Key: 'MUL', Default: null, Extra: '' },
      { Field: 'total', Type: 'decimal(10,2)', Null: 'NO', Key: '', Default: null, Extra: '' },
      { Field: 'status', Type: 'varchar(20)', Null: 'NO', Key: '', Default: 'pending', Extra: '' },
      { Field: 'created_at', Type: 'timestamp', Null: 'NO', Key: '', Default: 'CURRENT_TIMESTAMP', Extra: '' }
    ]
  } as TableStructures,
  sampleData: {
    users: [
      { id: 1, username: 'john_doe', email: '<EMAIL>', created_at: '2023-01-01 00:00:00' },
      { id: 2, username: 'jane_doe', email: '<EMAIL>', created_at: '2023-01-02 00:00:00' }
    ],
    products: [
      { id: 1, name: 'Laptop', price: 999.99, stock: 10 },
      { id: 2, name: 'Smartphone', price: 499.99, stock: 20 }
    ],
    orders: [
      { id: 1, user_id: 1, total: 999.99, status: 'completed', created_at: '2023-01-10 00:00:00' },
      { id: 2, user_id: 2, total: 499.99, status: 'pending', created_at: '2023-01-15 00:00:00' }
    ]
  } as SampleData
};

/**
 * Validate SQL query for security
 * Only allows SELECT, DESCRIBE, SHOW, and CREATE TABLE statements
 */
function validateSQL(sql: string): { valid: boolean; reason?: string } {
  try {
    // Normalize SQL by removing comments and extra whitespace
    const normalizedSQL = sql
      .replace(/--.*$/gm, '') // Remove single line comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
      .trim();
    
    // Basic validation for allowed statements
    const firstWord = normalizedSQL.split(' ')[0].toUpperCase();
    
    // 检查是否是允许的 SQL 操作类型
    if (['SELECT', 'DESCRIBE', 'DESC', 'SHOW', 'CREATE', 'EXPLAIN'].includes(firstWord)) {
      // For CREATE, ensure it's CREATE TABLE
      if (firstWord === 'CREATE') {
        if (!normalizedSQL.toUpperCase().startsWith('CREATE TABLE')) {
          return { 
            valid: false, 
            reason: 'Only CREATE TABLE statements are allowed' 
          };
        }
      }
      
      return { valid: true };
    }
    
    return { 
      valid: false, 
      reason: `Statement type '${firstWord}' is not allowed. Only SELECT, DESCRIBE, SHOW, EXPLAIN, and CREATE TABLE are permitted.` 
    };
  } catch (error) {
    console.error('[Error] SQL validation error:', error);
    return { 
      valid: false, 
      reason: 'SQL validation error' 
    };
  }
}

/**
 * Execute SQL query
 */
async function executeQuery(sql: string): Promise<any> {
  console.error('[SQL] Executing query:', sql);
  
  // Validate SQL
  const validation = validateSQL(sql);
  if (!validation.valid) {
    throw new McpError(
      ErrorCode.InvalidRequest,
      `Invalid SQL: ${validation.reason}`
    );
  }
  
  // If in mock mode, return mock data
  if (mockMode) {
    return handleMockQuery(sql);
  }
  
  // Ensure pool is initialized
  if (!pool) {
    throw new McpError(
      ErrorCode.InternalError,
      'Database connection not initialized'
    );
  }
  
  try {
    // Execute query
    const [rows] = await pool.query(sql);
    
    // Limit number of rows
    if (Array.isArray(rows) && rows.length > MAX_ROWS) {
      console.error(`[Warning] Query returned ${rows.length} rows, limiting to ${MAX_ROWS}`);
      return rows.slice(0, MAX_ROWS);
    }
    
    return rows;
  } catch (error: any) {
    console.error('[Error] Query execution failed:', error);
    throw new McpError(
      ErrorCode.InternalError,
      `Query execution failed: ${error.message}`
    );
  }
}

/**
 * Handle mock queries
 */
function handleMockQuery(sql: string): any {
  console.error('[Mock] Handling mock query:', sql);
  
  // Normalize SQL
  const normalizedSQL = sql.trim().toUpperCase();
  
  // Handle SHOW TABLES
  if (normalizedSQL === 'SHOW TABLES') {
    return mockData.tables.map(table => ({ Tables_in_test: table }));
  }
  
  // Handle DESCRIBE table
  const describeMatch = normalizedSQL.match(/^DESCRIBE\s+`?(\w+)`?$/i);
  if (describeMatch) {
    const tableName = describeMatch[1].toLowerCase();
    if (mockData.tableStructures[tableName]) {
      return mockData.tableStructures[tableName];
    }
    throw new McpError(
      ErrorCode.InternalError,
      `Table '${tableName}' not found`
    );
  }
  
  // Handle SELECT queries
  if (normalizedSQL.startsWith('SELECT')) {
    // Extract table name from simple queries
    const tableMatch = normalizedSQL.match(/FROM\s+`?(\w+)`?/i);
    if (tableMatch) {
      const tableName = tableMatch[1].toLowerCase();
      if (mockData.sampleData[tableName]) {
        return mockData.sampleData[tableName];
      }
    }
    
    // For complex queries, return empty array
    return [];
  }
  
  // Handle CREATE TABLE
  if (normalizedSQL.startsWith('CREATE TABLE')) {
    return { affectedRows: 0 };
  }
  
  // 新增：处理 EXPLAIN 查询的 Mock 逻辑
  if (normalizedSQL.startsWith('EXPLAIN')) {
    // 尝试从 EXPLAIN 语句中提取表名 (简单匹配)
    const explainTableMatch = normalizedSQL.match(/EXPLAIN\s+(?:SELECT\s+.*\s+FROM\s+)?\`?(\w+)\`?/i);
    let tableName = 'mock_table'; // 默认表名
    let mockRowCount = 2; // 默认模拟行数

    if (explainTableMatch && explainTableMatch[1]) {
      const extractedTableName = explainTableMatch[1].toLowerCase();
      if (mockData.sampleData[extractedTableName]) {
        tableName = extractedTableName;
        mockRowCount = mockData.sampleData[extractedTableName].length;
      }
    }

    // 返回一个模拟的 EXPLAIN 输出
    return [
      {
        id: 1,
        select_type: 'SIMPLE',
        table: tableName,
        partitions: null,
        type: 'ALL',
        possible_keys: null,
        key: null,
        key_len: null,
        ref: null,
        rows: mockRowCount, // 使用模拟数据中的行数或默认值
        filtered: 100.00,
        Extra: 'Mocked EXPLAIN result' // 模拟的额外信息
      }
    ];
  }
  
  // Default response
  return [];
}

/**
 * Create MCP server
 */
const server = new Server(
  {
    name: "mysql-mcp",
    version: "0.1.0",
  },
  {
    capabilities: {
      resources: {},
      tools: {},
    },
  }
);

/**
 * Handler for listing available database resources
 * Exposes tables as resources
 */
server.setRequestHandler(ListResourcesRequestSchema, async () => {
  try {
    // Get list of tables
    const tables = await executeQuery('SHOW TABLES');
    
    // Map tables to resources
    const resources = tables.map((row: any) => {
      const tableName = Object.values(row)[0] as string;
      return {
        uri: `mysql://${dbConfig.database}/${tableName}`,
        mimeType: "application/json",
        name: tableName,
        description: `MySQL table: ${tableName}`
      };
    });
    
    return { resources };
  } catch (error) {
    console.error('[Error] Failed to list resources:', error);
    return { resources: [] };
  }
});

/**
 * Handler for reading database resources
 * Returns table structure or sample data
 */
server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  try {
    const url = new URL(request.params.uri);
    const tableName = url.pathname.replace(/^\//, '');
    
    // Get table structure
    const structure = await executeQuery(`DESCRIBE \`${tableName}\``);
    
    // Get sample data (first 10 rows)
    const sampleData = await executeQuery(`SELECT * FROM \`${tableName}\` LIMIT 10`);
    
    // Combine structure and sample data
    const result = {
      table: tableName,
      structure,
      sampleData
    };
    
    return {
      contents: [{
        uri: request.params.uri,
        mimeType: "application/json",
        text: JSON.stringify(result, null, 2)
      }]
    };
  } catch (error) {
    console.error('[Error] Failed to read resource:', error);
    throw new McpError(
      ErrorCode.InternalError,
      `Failed to read resource: ${error}`
    );
  }
});

/**
 * Handler for listing available tools
 */
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "query_data",
        description: "Execute a SQL query and return results (SELECT, DESCRIBE, SHOW, EXPLAIN only)",
        inputSchema: {
          type: "object",
          properties: {
            sql: {
              type: "string",
              description: "SQL query to execute (SELECT, DESCRIBE, SHOW, EXPLAIN only)"
            }
          },
          required: ["sql"]
        }
      },
      {
        name: "describe_table",
        description: "Get table structure information",
        inputSchema: {
          type: "object",
          properties: {
            table: {
              type: "string",
              description: "Table name"
            }
          },
          required: ["table"]
        }
      },
      {
        name: "create_table",
        description: "Create a new table",
        inputSchema: {
          type: "object",
          properties: {
            sql: {
              type: "string",
              description: "CREATE TABLE SQL statement"
            }
          },
          required: ["sql"]
        }
      }
    ]
  };
});

/**
 * Handler for tool execution
 */
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    switch (request.params.name) {
      case "query_data": {
        const sql = String(request.params.arguments?.sql);
        if (!sql) {
          throw new McpError(
            ErrorCode.InvalidParams,
            "SQL query is required"
          );
        }
        
        const result = await executeQuery(sql);
        
        return {
          content: [{
            type: "text",
            text: JSON.stringify(result, null, 2)
          }]
        };
      }
      
      case "describe_table": {
        const table = String(request.params.arguments?.table);
        if (!table) {
          throw new McpError(
            ErrorCode.InvalidParams,
            "Table name is required"
          );
        }
        
        const result = await executeQuery(`DESCRIBE \`${table}\``);
        
        return {
          content: [{
            type: "text",
            text: JSON.stringify(result, null, 2)
          }]
        };
      }
      
      case "create_table": {
        const sql = String(request.params.arguments?.sql);
        if (!sql) {
          throw new McpError(
            ErrorCode.InvalidParams,
            "CREATE TABLE SQL statement is required"
          );
        }
        
        // Validate that this is a CREATE TABLE statement
        if (!sql.toUpperCase().trim().startsWith('CREATE TABLE')) {
          throw new McpError(
            ErrorCode.InvalidParams,
            "Only CREATE TABLE statements are allowed"
          );
        }
        
        const result = await executeQuery(sql);
        
        return {
          content: [{
            type: "text",
            text: `Table created successfully`
          }]
        };
      }
      
      default:
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`
        );
    }
  } catch (error) {
    console.error('[Error] Tool execution failed:', error);
    if (error instanceof McpError) {
      throw error;
    }
    throw new McpError(
      ErrorCode.InternalError,
      `Tool execution failed: ${error}`
    );
  }
});

/**
 * Start the server
 */
async function main() {
  console.error('[Setup] Starting MySQL MCP server...');
  
  // Initialize database connection
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('[Fatal] Failed to initialize database connection');
    process.exit(1);
  }
  
  // Connect server to transport
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('[Setup] MySQL MCP server running on stdio');
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('[Fatal] Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('[Fatal] Unhandled rejection:', reason);
  process.exit(1);
});

// Start server
main().catch((error) => {
  console.error('[Fatal] Server error:', error);
  process.exit(1);
});
