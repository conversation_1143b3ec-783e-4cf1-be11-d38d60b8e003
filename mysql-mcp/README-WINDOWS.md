# MySQL MCP for Windows (WSL2)

这个文档提供了如何在Windows环境下配置MySQL MCP的说明。

## 前提条件

- Windows 11 with WSL2 (Ubuntu 20.04)
- Node.js 已安装在WSL2环境中
- Cursor或Cline已安装在Windows环境中

## 配置步骤

1. 确保MySQL MCP已经构建完成：

```bash
cd /home/<USER>/projects/mysql-mcp/mysql-mcp
npm run build
```

2. 将MCP配置文件复制到Cursor/Cline配置目录：

### 对于Cursor

将`windows-cursor-mcp.json`文件中的内容复制到以下文件中：

```
C:\Users\<USER>\.cursor\mcp.json
```

如果该文件不存在，请创建它。

### 对于Cline

将`windows-cursor-mcp.json`文件中的内容复制到以下文件中：

```
C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json
```

如果该文件不存在，请创建它。

## 测试MySQL MCP

1. 重启Cursor/Cline
2. 在Cursor/Cline中，尝试以下命令：

```
请查询erp_dev数据库中的所有表
```

或者

```
请执行SQL查询：SHOW TABLES
```

## 故障排除

如果遇到问题，请检查：

1. WSL2是否正在运行
2. MySQL连接信息是否正确
3. 路径是否正确（特别是WSL2中的路径）

## 安全注意事项

配置文件中包含数据库凭据。请确保：

1. 不要将包含凭据的文件共享给他人
2. 考虑使用环境变量或其他安全方式存储凭据
